/* Tabs */
.fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel div.tabs p:not(.tab-title) {
    list-style-type: none;
  }
  .fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }
  .custom-theme.fr-popup .fr-colors-tabs {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab {
    color: #222222;
    padding: 8px 0;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:hover,
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:focus {
    color: #1e88e5;
  }
  .custom-theme.fr-popup
    .fr-colors-tabs
    .fr-colors-tab[data-param1="background"]::after {
    bottom: 0;
    left: 0;
    background: #1e88e5;
    -webkit-transition: transform none;
    -moz-transition: transform none;
    -ms-transition: transform none;
    -o-transition: transform none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab.fr-selected-tab {
    color: #1e88e5;
  }
  .fr-element.fr-view .tabs-panel .tabs .tab-title {
    list-style-type: circle;
    color: #0067c7;
    margin-bottom: unset;
    padding: 8px 12px;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    /* Create the text field appearance */
    box-shadow:
      inset 1px 1px 2px rgba(0, 0, 0, 0.1),      /* Top-left inner shadow */
      inset -1px -1px 1px rgba(255, 255, 255, 0.8); /* Bottom-right inner highlight */
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    position: relative;
    outline: none;
  }

  .fr-element.fr-view .tabs-panel .tabs .tab-title:hover {
    border-color: #9ca3af;
    box-shadow:
      inset 1px 1px 3px rgba(0, 0, 0, 0.15),     /* Slightly stronger shadow on hover */
      inset -1px -1px 1px rgba(255, 255, 255, 0.8);
  }

  .fr-element.fr-view .tabs-panel .tabs .tab-title.fr-selected-cell {
        outline: 1px solid #3b82f6;
        outline-offset: -8px;
        box-shadow: inset 0px 0px 0px 7px rgba(0, 0, 0, 0.1), inset -1px -1px 1px rgba(255, 255, 255, 0.8), 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }

  .removeBtn{
    border: none;
    background: white;
    padding: 9px 4px 4px 4px;
    border-radius: 3px;
  }