.fr-element.fr-view .accordion-panel {
    background: #f5f5f5;
  }
  .fr-element.fr-view .accordion-panel .accordion-header {
    color: #0067c7;
  }
  .fr-element.fr-view .accordion-panel {
    background: #f5f5f5;
  }
  .fr-element.fr-view .accordion-panel .accordion-header {
    color: #0067c7;
  }

  .removeBtn{
    border: none;
    background: white;
    padding: 9px 4px 4px 4px;
    border-radius: 3px;
  }
  
.fr-element.fr-view .accordion-panel .accordion-header .analytics-pill {
    display: inline-block;
    color: #4a90e2;
    font-weight: bold;
    border: 1px solid #4a90e2;
    background-color: white;
    width: 100%;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    cursor: pointer;
    transition: box-shadow 0.2s ease;
  }
  
  .fr-element.fr-view .accordion-panel .accordion-header .analytics-pill:hover,
  .fr-element.fr-view .accordion-panel .accordion-header .analytics-pill:focus {
    box-shadow: 0 0 0 2px #4a90e2;
    outline: none;
  }

  .fr-element.fr-view .accordion-panel .fr-selected-cell .analytics-pill {
    box-shadow: 0 0 0 2px #4a90e2;
    outline: none;
  }

  .fr-element.fr-view .accordion-panel .accordion-navigation .accordion .fr-selected-cell {
    outline: 1px solid #4a90e2;
    /* background-color: white; */
  }

  .fr-element.fr-view table.accordion-panel .accordion-navigation td.accordion-header::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-color: transparent !important;
  }