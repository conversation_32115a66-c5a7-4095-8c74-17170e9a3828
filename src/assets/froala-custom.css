/* Override the bad(?) styles from packages/local/boomtown-theme/sass/src/Fonts.scss */
.fr-element h1 {
    letter-spacing: unset;
}
.fr-element h2 {
    letter-spacing: unset;
    overflow: unset;
    text-overflow: unset;
    white-space: unset;
}

/* From Main.scss */
.fr-element p, .fr-view p, .fr-disbled p {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}
.fr-popup {
    z-index: 9999990 !important;
}
.fr-popup .fr-table-size .fr-select-table-size {
    text-align: center;
}
.fr-box.fr-basic .fr-element {
    min-height: 200px;
}

/* Fix border around editor */
.x-htmleditor-container.x-field .x-body-wrap-el {
    border: none !important;
}
.x-htmleditor-container .fr-toolbar.fr-sticky-on {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

/* More styles from SCSS files */
.kb-htmleditor .fr-wrapper {
    overflow-y: scroll;
}
.x-email-panel .x-htmleditor-container .x-body-wrap-el .fr-wrapper {
    border: none !important;
}
.x-email-panel .x-htmleditor-container .x-body-wrap-el .fr-toolbar {
    border: none !important;
    background-color: white !important;
    top: auto !important;
    bottom: 2px !important;
    position: absolute;
}

.clearfix::after {
    clear: both;
    display: block;
    content: "";
    height: 0;
}
.hide-by-clipping {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}
.custom-theme.fr-box.fr-basic .fr-element {
    color: #000000;
    padding: 4px;
    overflow-x: auto;
    min-height: 28px;
}
.custom-theme .fr-element {
    -webkit-user-select: auto;
}
.custom-theme.fr-box a.fr-floating-btn {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    height: 32px;
    width: 32px;
    background: #ffffff;
    color: #1e88e5;
    -webkit-transition: background none, color none, transform none;
    -moz-transition: background none, color none, transform none;
    -ms-transition: background none, color none, transform none;
    -o-transition: background none, color none, transform none;
    left: 0;
    top: 0;
    line-height: 32px;
    border: none;
}
.custom-theme.fr-box a.fr-floating-btn svg {
    -webkit-transition: transform none;
    -moz-transition: transform none;
    -ms-transition: transform none;
    -o-transition: transform none;
    fill: #1e88e5;
}
.custom-theme.fr-box a.fr-floating-btn i,
.custom-theme.fr-box a.fr-floating-btn svg {
    font-size: 14px;
    line-height: 32px;
}
.custom-theme.fr-box a.fr-floating-btn:hover {
    background: #e7e7e7;
}
.custom-theme.fr-box a.fr-floating-btn:hover svg {
    fill: #1e88e5;
}
.custom-theme .fr-wrapper .fr-placeholder {
    font-size: 12px;
    color: #aaaaaa;
    top: 0;
    left: 0;
    right: 0;
}
.custom-theme .fr-wrapper ::-moz-selection {
    background: #b5d6fd;
    color: #000000;
}
.custom-theme .fr-wrapper ::selection {
    background: #b5d6fd;
    color: #000000;
}
.custom-theme.fr-box.fr-basic .fr-wrapper {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-top: 0;
    top: 0;
    left: 0;
}
.custom-theme.fr-box.fr-basic.fr-top .fr-wrapper {
    border-top: 0;
    border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    -webkit-border-radius: 0 0 3px 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme.fr-box.fr-basic.fr-bottom .fr-wrapper {
    border-bottom: 0;
    border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
@media (min-width: 992px) {
    .custom-theme .fr-box.fr-document .fr-wrapper .fr-element {
        margin: auto;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }
}
.custom-theme .fr-sticky-on.fr-sticky-ios {
    left: 0;
    right: 0;
}
.custom-theme.fr-box .fr-counter {
    color: #cccccc;
    background: #ffffff;
    border-top: solid 1px #e7e7e7;
    border-left: solid 1px #e7e7e7;
    border-radius: 3px 0 0 0;
    -moz-border-radius: 3px 0 0 0;
    -webkit-border-radius: 3px 0 0 0;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme.fr-box.fr-rtl .fr-counter {
    right: auto;
    border-right: solid 1px #e7e7e7;
    border-radius: 0 3px 0 0;
    -moz-border-radius: 0 3px 0 0;
    -webkit-border-radius: 0 3px 0 0;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme textarea.fr-code {
    background: #ffffff;
    color: #000000;
}
.custom-theme.fr-box.fr-code-view.fr-inline {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme.fr-box.fr-inline .fr-command.fr-btn.html-switch {
    top: 0;
    right: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background: #fafafa;
    color: #222222;
    -moz-outline: 0;
    outline: 0;
    border: 0;
    padding: 12px 12px;
    -webkit-transition: background none;
    -moz-transition: background none;
    -ms-transition: background none;
    -o-transition: background none;
}
.custom-theme.fr-box.fr-inline .fr-command.fr-btn.html-switch i {
    font-size: 14px;
    width: 14px;
}
.custom-theme.fr-box.fr-inline .fr-command.fr-btn.html-switch.fr-desktop:hover {
    background: #e7e7e7;
}
.custom-theme.fr-popup .fr-colors-tabs {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab {
    color: #222222;
    padding: 8px 0;
}
.custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:hover,
.custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:focus {
    color: #1e88e5;
}
.custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab[data-param1="background"]::after {
    bottom: 0;
    left: 0;
    background: #1e88e5;
    -webkit-transition: transform none;
    -moz-transition: transform none;
    -ms-transition: transform none;
    -o-transition: transform none;
}
.custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab.fr-selected-tab {
    color: #1e88e5;
}
.custom-theme.fr-popup .fr-color-hex-layer .fr-input-line {
    padding: 8px 0 0;
}
.custom-theme.fr-popup .fr-color-hex-layer .fr-action-buttons button {
    background-color: #1e88e5;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme.fr-popup .fr-color-hex-layer .fr-action-buttons button:hover {
    background-color: #166dba;
}
.custom-theme.fr-popup .fr-color-set {
    line-height: 0;
}
.custom-theme.fr-popup .fr-color-set > span > i,
.custom-theme.fr-popup .fr-color-set > span > svg {
    bottom: 0;
    left: 0;
}
.custom-theme.fr-popup .fr-color-set > span .fr-selected-color {
    color: #fafafa;
    font-weight: 400;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
.custom-theme.fr-popup .fr-color-set > span:hover,
.custom-theme.fr-popup .fr-color-set > span:focus {
    outline: 1px solid #222222;
}
.custom-theme .fr-drag-helper {
    background: #1e88e5;
    z-index: 2147483640;
}
.custom-theme.fr-popup .fr-link:focus {
    outline: 0;
    background: #e7e7e7;
}
.custom-theme.fr-popup .fr-file-upload-layer {
    border: dashed 2px #b9b9b9;
    padding: 25px 0;
}
.custom-theme.fr-popup .fr-file-upload-layer:hover {
    background: #e7e7e7;
}
.custom-theme.fr-popup .fr-file-upload-layer.fr-drop {
    background: #e7e7e7;
    border-color: #1e88e5;
}
.custom-theme.fr-popup .fr-file-upload-layer .fr-form {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2147483640;
}
.custom-theme.fr-popup .fr-file-progress-bar-layer > h3 {
    margin: 10px 0;
}
.custom-theme.fr-popup .fr-file-progress-bar-layer > div.fr-loader {
    background: #b8d8f4;
}
.custom-theme.fr-popup .fr-file-progress-bar-layer > div.fr-loader span {
    background: #1e88e5;
    -webkit-transition: width none;
    -moz-transition: width none;
    -ms-transition: width none;
    -o-transition: width none;
}
.custom-theme.fr-popup .fr-file-progress-bar-layer > div.fr-loader.fr-indeterminate span {
    top: 0;
}
.custom-theme.fr-box.fr-fullscreen {
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table tr {
    border: 0;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table tbody tr {
    border-bottom: solid 1px #e7e7e7;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table tbody td:first-child {
    color: #636363;
}
.custom-theme .fr-image-resizer {
    border: solid 1px #1e88e5;
}
.custom-theme .fr-image-resizer .fr-handler {
    background: #1e88e5;
    border: solid 1px #fafafa;
}
.custom-theme .fr-image-resizer .fr-handler {
    width: 12px;
    height: 12px;
}
.custom-theme .fr-image-resizer .fr-handler.fr-hnw {
    left: -6px;
    top: -6px;
}
.custom-theme .fr-image-resizer .fr-handler.fr-hne {
    right: -6px;
    top: -6px;
}
.custom-theme .fr-image-resizer .fr-handler.fr-hsw {
    left: -6px;
    bottom: -6px;
}
.custom-theme .fr-image-resizer .fr-handler.fr-hse {
    right: -6px;
    bottom: -6px;
}
@media (min-width: 1200px) {
    .custom-theme .fr-image-resizer .fr-handler {
        width: 10px;
        height: 10px;
    }
    .custom-theme .fr-image-resizer .fr-handler.fr-hnw {
        left: -5px;
        top: -5px;
    }
    .custom-theme .fr-image-resizer .fr-handler.fr-hne {
        right: -5px;
        top: -5px;
    }
    .custom-theme .fr-image-resizer .fr-handler.fr-hsw {
        left: -5px;
        bottom: -5px;
    }
    .custom-theme .fr-image-resizer .fr-handler.fr-hse {
        right: -5px;
        bottom: -5px;
    }
}
.custom-theme.fr-image-overlay {
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 2147483640;
}
.custom-theme.fr-popup .fr-image-upload-layer {
    border: dashed 2px #b9b9b9;
    padding: 25px 0;
}
.custom-theme.fr-popup .fr-image-upload-layer:hover {
    background: #e7e7e7;
}
.custom-theme.fr-popup .fr-image-upload-layer.fr-drop {
    background: #e7e7e7;
    border-color: #1e88e5;
}
.custom-theme.fr-popup .fr-image-upload-layer .fr-form {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2147483640;
}
.custom-theme.fr-popup .fr-image-progress-bar-layer > h3 {
    margin: 10px 0;
}
.custom-theme.fr-popup .fr-image-progress-bar-layer > div.fr-loader {
    background: #b8d8f4;
}
.custom-theme.fr-popup .fr-image-progress-bar-layer > div.fr-loader span {
    background: #1e88e5;
    -webkit-transition: width none;
    -moz-transition: width none;
    -ms-transition: width none;
    -o-transition: width none;
}
.custom-theme.fr-popup .fr-image-progress-bar-layer > div.fr-loader.fr-indeterminate span {
    top: 0;
}
.custom-theme.fr-modal-head .fr-modal-head-line i.fr-modal-more {
    -webkit-transition: padding none, width none, opacity none;
    -moz-transition: padding none, width none, opacity none;
    -ms-transition: padding none, width none, opacity none;
    -o-transition: padding none, width none, opacity none;
}
.custom-theme.fr-modal-head .fr-modal-head-line i.fr-modal-more.fr-not-available {
    opacity: 0;
    width: 0;
    padding: 12px 0;
}
.custom-theme.fr-modal-head .fr-modal-tags a {
    opacity: 0;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    color: #1e88e5;
    -webkit-transition: opacity none, background none;
    -moz-transition: opacity none, background none;
    -ms-transition: opacity none, background none;
    -o-transition: opacity none, background none;
}
.custom-theme.fr-modal-head .fr-modal-tags a.fr-selected-tag {
    background: #d3d3d3;
}
.custom-themediv.fr-modal-body .fr-preloader {
    margin: 50px auto;
}
.custom-themediv.fr-modal-body div.fr-image-list {
    padding: 0;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container.fr-image-deleting::after {
    -webkit-transition: opacity none;
    -moz-transition: opacity none;
    -ms-transition: opacity none;
    -o-transition: opacity none;
    background: #000000;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container.fr-image-deleting::before {
    color: #fafafa;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty {
    background: #cccccc;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty::after {
    margin: auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container img {
    -webkit-transition: opacity none, filter none;
    -moz-transition: opacity none, filter none;
    -ms-transition: opacity none, filter none;
    -o-transition: opacity none, filter none;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img,
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img {
    -webkit-transition: background none, color none;
    -moz-transition: background none, color none;
    -ms-transition: background none, color none;
    -o-transition: background none, color none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    margin: 0;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img {
    background: #b8312f;
    color: #fafafa;
}
.custom-themediv.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img {
    background: #fafafa;
    color: #1e88e5;
}
.custom-theme.custom-theme.fr-desktop .fr-modal-wrapper .fr-modal-head .fr-modal-tags a:hover {
    background: #e7e7e7;
}
.custom-theme.custom-theme.fr-desktop .fr-modal-wrapper .fr-modal-head .fr-modal-tags a.fr-selected-tag {
    background: #d3d3d3;
}
.custom-theme.custom-theme.fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img:hover {
    background: #bf4543;
    color: #fafafa;
}
.custom-theme.custom-theme.fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img:hover {
    background: #e7e7e7;
}
.custom-theme .fr-line-breaker {
    border-top: 1px solid #1e88e5;
}
.custom-theme .fr-line-breaker a.fr-floating-btn {
    left: calc(34%);
    top: -16px;
}
.custom-theme .fr-qi-helper {
    padding-left: 4px;
}
.custom-theme .fr-qi-helper a.fr-btn.fr-floating-btn {
    color: #222222;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-body .fr-special-characters-modal .fr-special-character {
    border: 1px solid #cccccc;
}
.custom-theme .fr-element table td.fr-selected-cell,
.custom-theme .fr-element table th.fr-selected-cell {
    border: 1px double #1e88e5;
}
.custom-theme .fr-table-resizer div {
    border-right: 1px solid #1e88e5;
}
.custom-theme.fr-popup .fr-table-colors-hex-layer .fr-input-line {
    padding: 8px 0 0;
}
.custom-theme.fr-popup .fr-table-colors-hex-layer .fr-action-buttons button {
    background-color: #1e88e5;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme.fr-popup .fr-table-colors-hex-layer .fr-action-buttons button:hover {
    background-color: #166dba;
}
.custom-theme.fr-popup .fr-table-size .fr-select-table-size {
    line-height: 0;
}
.custom-theme.fr-popup .fr-table-size .fr-select-table-size > span {
    padding: 0px 4px 4px 0;
}
.custom-theme.fr-popup .fr-table-size .fr-select-table-size > span > span {
    border: 1px solid #dddddd;
}
.custom-theme.fr-popup .fr-table-size .fr-select-table-size > span.hover > span {
    background: rgba(30, 136, 229, 0.3);
    border: solid 1px #1e88e5;
}
.custom-theme.fr-popup .fr-table-colors {
    line-height: 0;
}
.custom-theme.fr-popup .fr-table-colors > span > i {
    bottom: 0;
    left: 0;
}
.custom-theme.fr-popup .fr-table-colors > span:focus {
    outline: 1px solid #222222;
}
.custom-theme .fr-element .fr-video::after {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
.custom-theme.fr-box .fr-video-resizer {
    border: solid 1px #1e88e5;
}
.custom-theme.fr-box .fr-video-resizer .fr-handler {
    background: #1e88e5;
    border: solid 1px #fafafa;
}
.custom-theme.fr-box .fr-video-resizer .fr-handler {
    width: 12px;
    height: 12px;
}
.custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hnw {
    left: -6px;
    top: -6px;
}
.custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hne {
    right: -6px;
    top: -6px;
}
.custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hsw {
    left: -6px;
    bottom: -6px;
}
.custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hse {
    right: -6px;
    bottom: -6px;
}
@media (min-width: 1200px) {
    .custom-theme.fr-box .fr-video-resizer .fr-handler {
        width: 10px;
        height: 10px;
    }
    .custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hnw {
        left: -5px;
        top: -5px;
    }
    .custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hne {
        right: -5px;
        top: -5px;
    }
    .custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hsw {
        left: -5px;
        bottom: -5px;
    }
    .custom-theme.fr-box .fr-video-resizer .fr-handler.fr-hse {
        right: -5px;
        bottom: -5px;
    }
}
.custom-theme.fr-popup .fr-video-upload-layer {
    border: dashed 2px #b9b9b9;
    padding: 25px 0;
}
.custom-theme.fr-popup .fr-video-upload-layer:hover {
    background: #e7e7e7;
}
.custom-theme.fr-popup .fr-video-upload-layer.fr-drop {
    background: #e7e7e7;
    border-color: #1e88e5;
}
.custom-theme.fr-popup .fr-video-upload-layer .fr-form {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2147483640;
}
.custom-theme.fr-popup .fr-video-progress-bar-layer > h3 {
    margin: 10px 0;
}
.custom-theme.fr-popup .fr-video-progress-bar-layer > div.fr-loader {
    background: #b8d8f4;
}
.custom-theme.fr-popup .fr-video-progress-bar-layer > div.fr-loader span {
    background: #1e88e5;
    -webkit-transition: width none;
    -moz-transition: width none;
    -ms-transition: width none;
    -o-transition: width none;
}
.custom-theme.fr-popup .fr-video-progress-bar-layer > div.fr-loader.fr-indeterminate span {
    top: 0;
}
.custom-theme.fr-video-overlay {
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 2147483640;
}
.custom-theme .fr-view span[style~="color:"] a {
    color: inherit;
}
.custom-theme .fr-view strong {
    font-weight: 700;
}
.custom-theme .fr-view table.fr-alternate-rows tbody tr:nth-child(2n) {
    background: #f5f5f5;
}
.custom-theme .fr-view table td,
.custom-theme .fr-view table th {
    border: 1px solid #dddddd;
}
.custom-theme .fr-view table th {
    background: #e1e1e1;
}
.custom-theme .fr-view[dir="rtl"] blockquote {
    border-right: solid 2px #5e35b1;
    margin-right: 0;
}
.custom-theme .fr-view[dir="rtl"] blockquote blockquote {
    border-color: #00bcd4;
}
.custom-theme .fr-view[dir="rtl"] blockquote blockquote blockquote {
    border-color: #43a047;
}
.custom-theme .fr-view blockquote {
    border-left: solid 2px #5e35b1;
    margin-left: 0;
    color: #5e35b1;
}
.custom-theme .fr-view blockquote blockquote {
    border-color: #00bcd4;
    color: #00bcd4;
}
.custom-theme .fr-view blockquote blockquote blockquote {
    border-color: #43a047;
    color: #43a047;
}
.custom-theme .fr-view span.fr-emoticon {
    line-height: 0;
}
.custom-theme .fr-view span.fr-emoticon.fr-emoticon-img {
    font-size: inherit;
}
.custom-theme .fr-view .fr-text-bordered {
    padding: 10px 0;
}
.custom-theme .fr-view .fr-class-highlighted {
    background-color: #ffff00;
}
.custom-theme .fr-view .fr-img-caption .fr-img-wrap {
    margin: auto;
}
.custom-theme .fr-view .fr-img-caption .fr-img-wrap img {
    margin: auto;
}
.custom-theme .fr-view .fr-img-caption .fr-img-wrap > span {
    margin: auto;
}
.custom-theme .fr-element .fr-embedly::after {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
.custom-theme.fr-box .fr-embedly-resizer {
    border: solid 1px #1e88e5;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-body .fr-font-awesome-modal .fr-font-awesome-title {
    border-bottom: solid 1px #ececec;
}
.custom-theme .tui-image-editor-container {
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10;
}
.custom-theme .tui-editor-cancel-btn {
    background-color: #fafafa;
    border: 1px solid #cccccc;
}
.custom-theme .tui-editor-save-btn {
    color: #fafafa;
}
.custom-theme .examples-variante > a {
    font-size: 14px;
    font-family: Arial, Helvetica, sans-serif;
}
.custom-theme .sc-cm-holder > .sc-cm {
    border-top: 1px solid transparent !important;
}
.custom-theme .sc-cm__item_dropdown:hover > a,
.custom-theme .sc-cm a:hover {
    background-color: #e7e7e7 !important;
}
.custom-theme .sc-cm__item_active > a,
.custom-theme .sc-cm__item_active > a:hover,
.custom-theme .sc-cm a:active,
.custom-theme .sc-cm a:focus {
    background-color: #d3d3d3 !important;
}
.custom-theme .sc-cm-holder > .sc-cm:before {
    background-color: #e7e7e7 !important;
}
.custom-theme .fr-tooltip {
    top: 0;
    left: 0;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    background: #222222;
    color: #ffffff;
    font-size: 11px;
    line-height: 22px;
    font-family: Arial, Helvetica, sans-serif;
    -webkit-transition: opacity none;
    -moz-transition: opacity none;
    -ms-transition: opacity none;
    -o-transition: opacity none;
}
.custom-theme.fr-toolbar .fr-command.fr-btn,
.custom-theme.fr-popup .fr-command.fr-btn {
    color: #222222;
    -moz-outline: 0;
    outline: 0;
    border: 0;
    margin: 0px 2px;
    -webkit-transition: background none;
    -moz-transition: background none;
    -ms-transition: background none;
    -o-transition: background none;
    padding: 0;
    width: 38px;
    height: 38px;
}
.custom-theme.fr-toolbar .fr-command.fr-btn::-moz-focus-inner,
.custom-theme.fr-popup .fr-command.fr-btn::-moz-focus-inner {
    border: 0;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-btn-text,
.custom-theme.fr-popup .fr-command.fr-btn.fr-btn-text {
    width: auto;
}
.custom-theme.fr-toolbar .fr-command.fr-btn i,
.custom-theme.fr-popup .fr-command.fr-btn i,
.custom-theme.fr-toolbar .fr-command.fr-btn svg,
.custom-theme.fr-popup .fr-command.fr-btn svg {
    font-size: 14px;
    width: 14px;
    margin: 12px 12px;
}
.custom-theme.fr-toolbar .fr-command.fr-btn span,
.custom-theme.fr-popup .fr-command.fr-btn span {
    font-size: 14px;
    line-height: 17px;
    min-width: 34px;
    height: 17px;
    padding: 0 2px;
}
.custom-theme.fr-toolbar .fr-command.fr-btn img,
.custom-theme.fr-popup .fr-command.fr-btn img {
    margin: 12px 12px;
    width: 14px;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-active,
.custom-theme.fr-popup .fr-command.fr-btn.fr-active {
    color: #1e88e5;
    background: transparent;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-selection,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown.fr-selection {
    width: auto;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown i,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown i,
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown span,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown span,
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown img,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown img,
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown svg,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown svg {
    margin-left: 8px;
    margin-right: 16px;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active {
    color: #222222;
    background: #d3d3d3;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:hover,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:hover,
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:focus,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:focus {
    background: #d3d3d3 !important;
    color: #222222 !important;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:hover::after,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:hover::after,
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:focus::after,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:focus::after {
    border-top-color: #222222 !important;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-dropdown::after,
.custom-theme.fr-popup .fr-command.fr-btn.fr-dropdown::after {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #222222;
    right: 4px;
    top: 17px;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-disabled,
.custom-theme.fr-popup .fr-command.fr-btn.fr-disabled {
    color: #b9b9b9;
}
.custom-theme.fr-toolbar .fr-command.fr-btn.fr-disabled::after,
.custom-theme.fr-popup .fr-command.fr-btn.fr-disabled::after {
    border-top-color: #b9b9b9 !important;
}
.custom-theme.fr-toolbar.fr-disabled .fr-btn,
.custom-theme.fr-popup.fr-disabled .fr-btn,
.custom-theme.fr-toolbar.fr-disabled .fr-btn.fr-active,
.custom-theme.fr-popup.fr-disabled .fr-btn.fr-active {
    color: #b9b9b9;
}
.custom-theme.fr-toolbar.fr-disabled .fr-btn.fr-dropdown::after,
.custom-theme.fr-popup.fr-disabled .fr-btn.fr-dropdown::after,
.custom-theme.fr-toolbar.fr-disabled .fr-btn.fr-active.fr-dropdown::after,
.custom-theme.fr-popup.fr-disabled .fr-btn.fr-active.fr-dropdown::after {
    border-top-color: #b9b9b9;
}
.custom-theme.fr-desktop .fr-command:hover,
.custom-theme.fr-desktop .fr-command:focus,
.custom-theme.fr-desktop .fr-command.fr-btn-hover,
.custom-theme.fr-desktop .fr-command.fr-expanded {
    outline: 0;
    color: #222222;
    background: #e7e7e7;
}
.custom-theme.fr-desktop .fr-command:hover::after,
.custom-theme.fr-desktop .fr-command:focus::after,
.custom-theme.fr-desktop .fr-command.fr-btn-hover::after,
.custom-theme.fr-desktop .fr-command.fr-expanded::after {
    border-top-color: #222222 !important;
}
.custom-theme.fr-desktop .fr-command.fr-selected {
    color: #222222;
    background: #d3d3d3;
}
.custom-theme.fr-desktop .fr-command.fr-active:hover,
.custom-theme.fr-desktop .fr-command.fr-active:focus,
.custom-theme.fr-desktop .fr-command.fr-active.fr-btn-hover,
.custom-theme.fr-desktop .fr-command.fr-active.fr-expanded {
    color: #1e88e5;
    background: #e7e7e7;
}
.custom-theme.fr-desktop .fr-command.fr-active.fr-selected {
    color: #1e88e5;
    background: #d3d3d3;
}
.custom-theme.fr-toolbar.fr-mobile .fr-command.fr-blink,
.custom-theme.fr-popup.fr-mobile .fr-command.fr-blink {
    background: transparent;
}
.custom-theme .fr-command.fr-btn.fr-options {
    width: 16px;
    margin-left: -5px;
}
.custom-theme .fr-command.fr-btn.fr-options.fr-btn-hover,
.custom-theme .fr-command.fr-btn.fr-options:hover,
.custom-theme .fr-command.fr-btn.fr-options:focus {
    border-left: solid 1px #fafafa;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu {
    right: auto;
    bottom: auto;
    height: auto;
    border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    -webkit-border-radius: 0 0 3px 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu.test-height .fr-dropdown-wrapper {
    height: auto;
    max-height: 275px;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper {
    background: #fafafa;
    padding: 0;
    margin: auto;
    -webkit-transition: max-height none;
    -moz-transition: max-height none;
    -ms-transition: max-height none;
    -o-transition: max-height none;
    margin-top: 0;
    max-height: 0;
    height: 0;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper .fr-dropdown-content {
    overflow: auto;
    max-height: 275px;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper .fr-dropdown-content ul.fr-dropdown-list {
    margin: 0;
    padding: 0;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper .fr-dropdown-content ul.fr-dropdown-list li {
    padding: 0;
    margin: 0;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper .fr-dropdown-content ul.fr-dropdown-list li a {
    color: inherit;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper .fr-dropdown-content ul.fr-dropdown-list li a.fr-active {
    background: #d3d3d3;
}
.custom-theme .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper .fr-dropdown-content ul.fr-dropdown-list li a.fr-disabled {
    color: #b9b9b9;
}
.custom-theme .fr-command.fr-btn.fr-active + .fr-dropdown-menu {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme .fr-command.fr-btn.fr-active + .fr-dropdown-menu .fr-dropdown-wrapper {
    height: auto;
    max-height: 275px;
}
.custom-theme .fr-bottom > .fr-command.fr-btn + .fr-dropdown-menu {
    border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme.fr-modal {
    color: #222222;
    font-family: Arial, Helvetica, sans-serif;
    overflow-x: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 2147483640;
}
.custom-theme.fr-modal.fr-middle .fr-modal-wrapper {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
}
.custom-theme.fr-modal .fr-modal-wrapper {
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    margin: 20px auto;
    background: #fafafa;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border: 1px solid #e7e7e7;
    border-top: 3px solid transparent;
}
@media (min-width: 768px) and (max-width: 991px) {
    .custom-theme.fr-modal .fr-modal-wrapper {
        margin: 30px auto;
    }
}
@media (min-width: 992px) {
    .custom-theme.fr-modal .fr-modal-wrapper {
        margin: 50px auto;
    }
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-head {
    background: #fafafa;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-bottom: 1px solid #e7e7e7;
    -webkit-transition: height none;
    -moz-transition: height none;
    -ms-transition: height none;
    -o-transition: height none;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-close {
    color: #222222;
    top: 0;
    right: 0;
    -webkit-transition: color none;
    -moz-transition: color none;
    -ms-transition: color none;
    -o-transition: color none;
}
.custom-theme.fr-modal .fr-modal-wrapper .fr-modal-head h4 {
    margin: 0;
    font-weight: 400;
}
.custom-theme.fr-modal .fr-modal-wrapper div.fr-modal-body:focus {
    outline: 0;
}
.custom-theme.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command {
    color: #1e88e5;
    -webkit-transition: background none;
    -moz-transition: background none;
    -ms-transition: background none;
    -o-transition: background none;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:hover,
.custom-theme.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:focus {
    background: #e7e7e7;
    color: #1e88e5;
}
.custom-theme.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:active {
    background: #d3d3d3;
    color: #1e88e5;
}
.custom-theme.fr-modal .fr-modal-wrapper div.fr-modal-body button::-moz-focus-inner {
    border: 0;
}
.custom-theme.custom-theme.fr-desktop .fr-modal-wrapper .fr-modal-head i:hover {
    background: #e7e7e7;
}
.custom-theme.fr-overlay {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: #000000;
}
.custom-theme.fr-popup {
    color: #222222;
    background: #fafafa;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    font-family: Arial, Helvetica, sans-serif;
    border: 1px solid #e7e7e7;
    border-top: 3px solid transparent;
}
.custom-theme.fr-popup .fr-input-focus {
    background: #f0f0f0;
}
.custom-theme.fr-popup.fr-above {
    border-top: 0;
    border-bottom: 3px solid transparent;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme.fr-popup .fr-input-line {
    padding: 8px 0;
}
.custom-theme.fr-popup .fr-input-line input[type="text"],
.custom-theme.fr-popup .fr-input-line textarea {
    margin: 0px 0 1px 0;
    border-bottom: solid 1px #b9b9b9;
    color: #222222;
}
.custom-theme.fr-popup .fr-input-line input[type="text"]:focus,
.custom-theme.fr-popup .fr-input-line textarea:focus {
    border-bottom: solid 2px #1e88e5;
}
.custom-theme.fr-popup .fr-input-line input + label,
.custom-theme.fr-popup .fr-input-line textarea + label {
    top: 0;
    left: 0;
    -webkit-transition: color none;
    -moz-transition: color none;
    -ms-transition: color none;
    -o-transition: color none;
    background: #fafafa;
}
.custom-theme.fr-popup .fr-input-line input.fr-not-empty:focus + label,
.custom-theme.fr-popup .fr-input-line textarea.fr-not-empty:focus + label {
    color: #1e88e5;
}
.custom-theme.fr-popup .fr-input-line input.fr-not-empty + label,
.custom-theme.fr-popup .fr-input-line textarea.fr-not-empty + label {
    color: #7d7d7d;
}
.custom-theme.fr-popup .fr-buttons {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    padding: 0 2px;
    line-height: 0;
    border-bottom: 1px solid #e7e7e7;
}
.custom-theme.fr-popup .fr-layer {
    width: 225px;
}
@media (min-width: 768px) {
    .custom-theme.fr-popup .fr-layer {
        width: 300px;
    }
}
.custom-theme.fr-popup .fr-action-buttons button.fr-command {
    color: #1e88e5;
    -webkit-transition: background none;
    -moz-transition: background none;
    -ms-transition: background none;
    -o-transition: background none;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
}
.custom-theme.fr-popup .fr-action-buttons button.fr-command:hover,
.custom-theme.fr-popup .fr-action-buttons button.fr-command:focus {
    background: #e7e7e7;
    color: #1e88e5;
}
.custom-theme.fr-popup .fr-action-buttons button.fr-command:active {
    background: #d3d3d3;
    color: #1e88e5;
}
.custom-theme.fr-popup .fr-action-buttons button::-moz-focus-inner {
    border: 0;
}
.custom-theme.fr-popup .fr-checkbox span {
    border: solid 1px #222222;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-transition: background none, border-color none;
    -moz-transition: background none, border-color none;
    -ms-transition: background none, border-color none;
    -o-transition: background none, border-color none;
}
.custom-theme.fr-popup .fr-checkbox input {
    margin: 0;
    padding: 0;
}
.custom-theme.fr-popup .fr-checkbox input:checked + span {
    background: #1e88e5;
    border-color: #1e88e5;
}
.custom-theme.fr-popup .fr-checkbox input:focus + span {
    border-color: #1e88e5;
}
.custom-theme.fr-popup.fr-rtl .fr-input-line input + label,
.custom-theme.fr-popup.fr-rtl .fr-input-line textarea + label {
    left: auto;
    right: 0;
}
.custom-theme.fr-popup .fr-arrow {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #222222;
    top: -9px;
    margin-left: -5px;
}
.custom-theme.fr-popup.fr-above .fr-arrow {
    top: auto;
    bottom: -9px;
    border-bottom: 0;
    border-top: 5px solid #222222;
}
.custom-theme.fr-toolbar {
    color: #222222;
    background: #fafafa;
    font-family: Arial, Helvetica, sans-serif;
    padding: 0 2px;
    border-radius: 3px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border: 1px solid #e7e7e7;
    border-top: 3px solid transparent;
}
.custom-theme.fr-toolbar.fr-inline .fr-arrow {
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #222222;
    top: -9px;
    margin-left: -5px;
}
.custom-theme.fr-toolbar.fr-inline.fr-above {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    border-bottom: 3px solid transparent;
    border-top: 0;
}
.custom-theme.fr-toolbar.fr-inline.fr-above .fr-arrow {
    top: auto;
    bottom: -9px;
    border-bottom: 0;
    border-top-color: inherit;
    border-top-width: 5px;
}
.custom-theme.fr-toolbar.fr-top {
    top: 0;
    border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme.fr-toolbar.fr-bottom {
    bottom: 0;
    border-radius: 0 0 3px 3px;
    -moz-border-radius: 0 0 3px 3px;
    -webkit-border-radius: 0 0 3px 3px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.custom-theme .fr-separator {
    background: #e7e7e7;
}
.custom-theme .fr-separator.fr-vs {
    height: 34px;
    width: 1px;
    margin: 2px;
}
.custom-theme .fr-separator.fr-hs {
    height: 1px;
    width: calc(96%);
    margin: 0 2px;
}

/* Normal font*/
.fr-element.fr-view, .fr-element.fr-view p {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    /*margin-top: 10px*/
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 21px;
    color: #282a2c;
}
/* Italic */
.fr-element.fr-view em {
    font-family: 'Inter', sans-serif;
}
/* Bold*/
.fr-element.fr-view strong, .fr-element.fr-view strong em {
    font-family: 'Inter', sans-serif;
}
/* Headings */
.fr-element.fr-view h1, .fr-element.fr-view h2, .fr-element.fr-view h3, .fr-element.fr-view h4 {
    font-family: 'Inter', sans-serif;
    font-weight: 600 !important;
}
.fr-element.fr-view h1 {
    /*margin-top: 20px;*/
    margin-bottom: 14px;
    font-size: 20px;
    line-height: 30px;
    color: #282a2c;
    letter-spacing: 0.03px;
}
.fr-element.fr-view h2 {
    /*margin-top: 18px;*/
    margin-bottom: 14px;
    font-size: 18px;
    line-height: 27px;
    color: #282a2c;
}
.fr-element.fr-view h3 {
    /*margin-top: 16px;*/
    margin-bottom: 14px;
    font-size: 16px;
    line-height: 24px;
    color: #282a2c;
}
.fr-element.fr-view h4 {
    /*margin-top: 15px;*/
    margin-bottom: 14px;
    font-size: 15px;
    line-height: 21px;
    color: #282a2c;
}
/* Code */
.fr-element.fr-view pre {
    /*padding-top: 14px;*/
    /*padding-bottom: 14px;*/
    font-size: 14px;
    line-height: 21px;
    color: #626363;

    border-color: #cccccc;
    border-radius: 2px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    background: #f5f5f5;
    padding: 10px;
    font-family: "Courier New", Courier, monospace;
}

/* Text alignment menu */
.fr-dropdown-menu[id^="dropdown-menu-align"] div.fr-dropdown-wrapper {
    height: auto;
}
.fr-dropdown-menu[id^="dropdown-menu-align"] ul li:first-child {
    padding-top: 10px !important;
}
.fr-dropdown-menu[id^="dropdown-menu-align"] ul li {
    padding-bottom: 10px !important;
}
.fr-dropdown-menu[id^="dropdown-menu-align"] ul li i {
    font-size: 20px;
}

/* Figure (image/video) alignment menu */
.fr-dropdown-menu[id^="dropdown-menu-figureAlign"] div.fr-dropdown-wrapper {
    height: auto;
}
.fr-dropdown-menu[id^="dropdown-menu-figureAlign"] ul li:first-child {
    padding-top: 10px !important;
}
.fr-dropdown-menu[id^="dropdown-menu-figureAlign"] ul li {
    padding-bottom: 10px !important;
}
.fr-dropdown-menu[id^="dropdown-menu-figureAlign"] ul li i {
    font-size: 20px;
}

/* tables, adapted from kb.css */
.fr-element.fr-view table {
    width: 100% !important;
}
.fr-element.fr-view table,
.fr-element.fr-view table th,
.fr-element.fr-view table th tr,
.fr-element.fr-view table tr,
.fr-element.fr-view table tr td {
    background-color: transparent;
}
.fr-element.fr-view table thead,
.fr-element.fr-view table.fr-alternate-rows tbody tr:nth-child(2n) td {
    background: #f5f5f5;
}
.fr-element.fr-view table th.fr-highlighted,
.fr-element.fr-view table td.fr-highlighted {
    background-color: #fdf1d3 !important;
}
.fr-element.fr-view table.fr-dashed-borders td,
.fr-element.fr-view table.fr-dashed-borders th {
    border-style: dashed;
}
.fr-element.fr-view table td {
    border: 1px solid #ccc;
}
.fr-element.fr-view table th.fr-thick,
.fr-element.fr-view table td.fr-thick {
    border: 2px solid #ccc;
}
.fr-element.fr-view table th.fr-borderless,
.fr-element.fr-view table td.fr-borderless {
    border: 0 none;
}

/* Tabs */
.fr-element.fr-view .tabs-panel ul.tabs li.tab-title {
    list-style-type: circle;
}
.fr-element.fr-view .tabs-panel ul.tabs li:not(.tab-title) {
    list-style-type: none;
}
.fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067C7;
}
.fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
}
.fr-element.fr-view .accordion-panel {
    background: #f5f5f5;
}
.fr-element.fr-view .accordion-panel .accordion-header{
    color: #0067C7;
}

.fr-element.fr-view .tabs-panel .tabs .tab-title {
    list-style-type: circle;
    color: #0067C7;
    margin-bottom: unset;
    padding-bottom: 10px;
    padding-top: 10px;
}
.fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
}
.fr-element.fr-view .accordion-panel {
    background: #f5f5f5;
}
.fr-element.fr-view .accordion-panel .accordion-header{
    color: #0067C7;
}

/* Email Editor (comms) Only */
.x-email-panel .fr-element.fr-view p {
    font-size: 13px !important;
    line-height: 17px;
}
/* Fix toolbar menus for email comms panel */
.x-email-panel .fr-box .fr-toolbar {
    width: 100%;
}
.x-email-panel [id^="dropdown-menu-formatOLOptions-"].fr-dropdown-menu {
    left: -84px !important;
    bottom: -15px !important;
}
.x-email-panel [id^="dropdown-menu-formatULOptions-"].fr-dropdown-menu {
    left: 28px !important;
    bottom: 28px !important;
}
.x-email-panel .fr-toolbar .fr-popup {
    left: 124px !important;
    top: -206px !important;
}
.x-email-panel .fr-toolbar.fr-toolbar-open .fr-popup {
    top: -160px !important;
}
.x-email-panel .fr-popup .fr-link-insert-layer {
    margin: 5px;
}
.x-email-panel .fr-placeholder, .x-email-panel .fr-element.fr-view {
    padding-top: 10px !important;
    padding-left: 3px !important;
}
/* Remove blank spaces between "groups" on email toolbar */
.x-email-panel .fr-toolbar .fr-btn-grp {
    margin: 0;
}

/* Fix shadows on images */
img.fr-shadow,
.fr-img-caption.fr-shadow img {
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 1px 1px rgba(0, 0, 0, 0.16);
    -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 1px 1px rgba(0, 0, 0, 0.16);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 1px 1px rgba(0, 0, 0, 0.16);
}

.fr-element.fr-view ul, .fr-element.fr-view ol {
    margin-left: 2.1rem;
}

/* Captions on images/videos */
.fr-element.fr-view figure.fr-figure-left {
    float: left;
}
.fr-element.fr-view figure.fr-figure-right {
    float: right;
}

.fr-element.fr-view figure {
    display: table;
    text-align: center;
    margin: 0 auto;
}

.fr-element.fr-view figure figcaption {
    display: table-caption;
    caption-side: bottom;
}

/* Insert KB modal */
.fr-popup .kbTreeSearch {
    border: 1px #898D94 solid;
    border-radius: 5px;
    margin: 0 10px 10px 10px;
}

.fr-popup .kbTreeSearch.x-focused {
    border-color: #0067C7;
}

.fr-popup .kbTreeSearch.x-textfield {
    padding-left: 10px;
    padding-bottom: 5px;
}

.fr-popup .kbTreeSearch.x-tree .x-body-el {
    margin-left: -5px;
}

.fr-popup .kbTreeSearch .x-listitem.x-gridrow {
    background-color: transparent;
    margin-left: -5px;
    padding: 10px 0 !important;
}


.fr-popup .kbTreeSearch .x-listitem.x-gridrow.x-hovered {
    background-color: #E1F5FE;
}

.fr-popup .kbTreeSearch .x-listitem.x-gridrow .x-body-el {
     text-overflow: unset;
     overflow: visible;
}

.fr-popup .kbTreeSearch .x-headercontainer {
    /*display: none;*/
    height: 0;
}

.fr-popup .fr-layer.fr-kb-insert-layer,
.fr-popup .fr-layer.fr-kb-insert-layer .fr-checkbox-line {
    margin: 0;
}

.fr-popup .fr-layer.fr-kb-insert-layer {
    padding-left: 5px;
    margin-top: -5px;
}

.kbTreeHighlight {
    background-color: #FFD7A1;
}

.fr-popup .kbTreeSearch .x-input-el::placeholder,
.fr-popup .kbTreeSearch .x-treecell.x-gridcell .x-expander-el,
.fr-popup .kbTreeSearch .x-treecell.x-gridcell .x-font-icon.icon,
.fr-popup .kbTreeSearch .x-treecell.x-gridcell .x-treecell-body-el {
    color: #878C92 !important;
}

.fr-popup .kbTreeSearch .x-treecell.x-gridcell.x-leaf .x-treecell-body-el {
    color: #303336 !important;
}

.fr-popup .kbTreeSearch .x-treecell.x-gridcell .x-font-icon.icon {
    margin-left: 0;
}

.fr-popup .kbTreeSearch .x-treecell.x-gridcell .x-treecell-body-el {
    margin-left: 1px;
    white-space: normal;
}
.fr-toolbar.fr-top, .fr-second-toolbar, .fr-box.fr-basic .fr-wrapper {
    border: 0;
}

.fr-toolbar.fr-top {
    position: sticky;
    z-index: 2;
}

.fr-element.fr-view ul, .fr-element.fr-view ol {
    list-style-type: disc;
}

.fr-element.fr-view ol {
    list-style-type: decimal;
}

.fr-box .fr-visible a.fr-floating-btn {
    display: flex;
}

.fr-box .fr-visible a.fr-floating-btn .fr-btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.fr-qi-helper {
    display: flex;
}

.fr-qi-helper a.fr-btn.fr-floating-btn {
    display: flex;
    align-items: center;
    justify-content: center;
}

.fr-qi-helper a.fr-btn.fr-floating-btn .icon, .fr-qi-helper a.fr-btn.fr-floating-btn svg{
    padding-top: 5px;
}

.fr-box.fr-basic .fr-element {
    font-family: 'Inter', sans-serif;
}

/* Enhanced Accordion Styling */
.fr-element.fr-view table.accordion-panel {
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin: 12px 0;
    border-collapse: separate;
    border-spacing: 0;
    overflow: hidden;
}

.fr-element.fr-view table.accordion-panel td.accordion-header {
    background: #f8f9fa;
    color: #0067c7;
    font-weight: 600;
    padding: 12px 16px;
    border: none;
    font-size: 14px;
}

.fr-element.fr-view table.accordion-panel tr:first-child td.accordion-header {
    border-radius: 6px 6px 0 0;
}

.fr-element.fr-view table.accordion-panel td.accordion-header:hover {
    background: #e9ecef;
}

.fr-element.fr-view table.accordion-panel td.content {
    background: white;
    padding: 16px;
    border-top: 1px solid #dee2e6;
    line-height: 1.5;
    color: #495057;
}

.fr-element.fr-view table.accordion-panel tr:last-child td.content {
    border-radius: 0 0 6px 6px;
}

/* Enhanced Tab Styling */
.fr-element.fr-view table.tabs-panel {
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin: 12px 0;
    border-collapse: separate;
    border-spacing: 0;
    overflow: hidden;
    outline: 1px solid #dad4d4;
    box-shadow: 0 0 0 2px rgb(232 219 219 / 50%);
}

.fr-element.fr-view table.tabs-panel tr.tabs {
    background: #f8f9fa;
}

.fr-element.fr-view table.tabs-panel tr.tabs td:first-child {
    border-radius: 6px 0 0 0;
}

.fr-element.fr-view table.tabs-panel tr.tabs td:last-child {
    border-radius: 0 6px 0 0;
}

.fr-element.fr-view table.tabs-panel td.tab-title {
    background: #f8f9fa;
    color: #0067c7;
    font-weight: 600;
    padding: 12px 16px;
    border: none;
    border-right: 1px solid #dee2e6;
    font-size: 14px;
    text-align: center;
}

.fr-element.fr-view table.tabs-panel td.tab-title:last-child {
    border-right: none;
}

.fr-element.fr-view table.tabs-panel td.tab-title:hover {
    background: #e9ecef;
}

.fr-element.fr-view table.tabs-panel tr.tabs-content td.content {
    background: white;
    padding: 16px;
    border-top: 1px solid #dee2e6;
    line-height: 1.5;
    color: #495057;
    min-height: 100px;
}

.fr-element.fr-view table.tabs-panel tr:last-child td:first-child {
    border-radius: 0 0 0 6px;
}

.fr-element.fr-view table.tabs-panel tr:last-child td:last-child {
    border-radius: 0 0 6px 0;
}

/* Responsive Design for Tabs and Accordions */
@media (max-width: 768px) {
    .fr-element.fr-view .accordion-panel .accordion-header {
        padding: 10px 12px;
        font-size: 13px;
    }

    .fr-element.fr-view .accordion-panel .content {
        padding: 12px;
    }

    .fr-element.fr-view .tabs-panel .tab-title {
        padding: 10px 12px;
        font-size: 13px;
    }

    .fr-element.fr-view .tabs-panel .tabs-content .content {
        padding: 12px;
        min-height: 80px;
    }
}

/* Focus states for accessibility */
.fr-element.fr-view .accordion-panel .accordion-header:focus,
.fr-element.fr-view .tabs-panel .tab-title:focus {
    outline: 2px solid #0067c7;
    outline-offset: 2px;
}

/* Embedded article edit block */
 .is-editing div.bt-block-article, .is-editing span.bt-block-article {
	 border: solid 1px #dedede;
	 border-radius: 3px;
	 margin: 4px;
	 min-height: 45px;
}
 .is-editing div.bt-block-article.bt-block-link-only, .is-editing span.bt-block-article.bt-block-link-only {
	 border: none;
	 margin: 0;
	 display: inline-block;
}
 .is-editing div.bt-block-article.bt-block-link-only > .bt-block-title, .is-editing span.bt-block-article.bt-block-link-only > .bt-block-title {
	 padding: 0;
}
 .is-editing div.bt-block-article.bt-block-link-only.bt-block-substituted, .is-editing span.bt-block-article.bt-block-link-only.bt-block-substituted {
	 border: none;
	 border-radius: unset;
}
 .is-editing div.bt-block-article > .bt-block-title, .is-editing span.bt-block-article > .bt-block-title {
	 padding: 8px;
	 display: flex;
}
 .is-editing div.bt-block-article > .bt-block-title > span, .is-editing span.bt-block-article > .bt-block-title > span {
	 font-weight: bold;
}
 .is-editing div.bt-block-article > .bt-block-title > span.bt-title-link, .is-editing span.bt-block-article > .bt-block-title > span.bt-title-link {
	 color: #005aad;
	 font-weight: 600;
}
 .is-editing div.bt-block-article > .bt-block-title > span.bt-title-link:hover, .is-editing span.bt-block-article > .bt-block-title > span.bt-title-link:hover {
	 text-decoration: underline;
}
 .is-editing div.bt-block-article > .bt-block-title > span.spacer, .is-editing span.bt-block-article > .bt-block-title > span.spacer {
	 flex: 1;
}
 .is-editing div.bt-block-article > .bt-block-inner, .is-editing span.bt-block-article > .bt-block-inner {
	 display: inline-block;
	 padding: 0 12px 12px 12px;
}
 .is-editing div.bt-block-article > .bt-block-inner > .summary, .is-editing span.bt-block-article > .bt-block-inner > .summary {
	 display: block;
}
 .is-editing div.bt-block-article > .bt-block-inner .bt-block-section-title, .is-editing span.bt-block-article > .bt-block-inner .bt-block-section-title {
	 font-weight: bold;
}
 .is-editing div.bt-block-article span.chips span.chip, .is-editing span.bt-block-article span.chips span.chip, .is-editing div.bt-block-article .summary span.chip, .is-editing span.bt-block-article .summary span.chip {
	 display: inline-block;
	 background-color: #ddf5f5;
	 padding: 2px 4px;
	 border-radius: 4px;
	 white-space: nowrap;
	 margin: 0 2px;
}
 .is-editing div.bt-block-article span.chips span.chip.chip-light-red, .is-editing span.bt-block-article span.chips span.chip.chip-light-red, .is-editing div.bt-block-article .summary span.chip.chip-light-red, .is-editing span.bt-block-article .summary span.chip.chip-light-red {
	 background-color: #f7af86;
}
 .is-editing div.bt-block-article span.chips span.chip.chip-light-yellow, .is-editing span.bt-block-article span.chips span.chip.chip-light-yellow, .is-editing div.bt-block-article .summary span.chip.chip-light-yellow, .is-editing span.bt-block-article .summary span.chip.chip-light-yellow {
	 background-color: #fce8b2;
}
 .is-editing div.bt-block-article span.chips span.chip.chip-light-blue, .is-editing span.bt-block-article span.chips span.chip.chip-light-blue, .is-editing div.bt-block-article .summary span.chip.chip-light-blue, .is-editing span.bt-block-article .summary span.chip.chip-light-blue {
	 background-color: #ddf5f5;
}
 .is-editing div.bt-block-article span.chips span.chip.chip-light-purple, .is-editing span.bt-block-article span.chips span.chip.chip-light-purple, .is-editing div.bt-block-article .summary span.chip.chip-light-purple, .is-editing span.bt-block-article .summary span.chip.chip-light-purple {
	 background-color: #e9e5fb;
}
 .is-editing div.bt-block-article span.chips span.chip.chip-light-green, .is-editing span.bt-block-article span.chips span.chip.chip-light-green, .is-editing div.bt-block-article .summary span.chip.chip-light-green, .is-editing span.bt-block-article .summary span.chip.chip-light-green {
	 background-color: #b7eacc;
}
 .is-editing div.bt-block-article span.chips span.chip.chip-transparent, .is-editing span.bt-block-article span.chips span.chip.chip-transparent, .is-editing div.bt-block-article .summary span.chip.chip-transparent, .is-editing span.bt-block-article .summary span.chip.chip-transparent {
	 background-color: unset !important;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-title {
	 padding: 12px;
	 display: flex;
	 font-weight: bold;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-title > span.spacer {
	 flex: 1;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-title > i, .is-editing .bt-block-rendered-outer > .bt-block-rendered-title > a > i {
	 margin-left: 12px;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-inner {
	 padding: 0 12px;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-inner > .body {
	 padding: 12px 0;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-inner > .summary {
	 font-size: smaller;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-inner > .summary > span {
	 color: #9f9f9f;
}
 .is-editing .bt-block-rendered-outer > .bt-block-rendered-inner > .summary > .tags {
	 display: inline;
}
 .is-editing .collapsable > .head > .control {
	 cursor: pointer;
}
 .is-editing .collapsable > .head .control:before {
	 content: "\e930";
}
 .is-editing .collapsable.collapsed > .head .control:before {
	 content: "\f078";
}
 .is-editing .collapsable.collapsed > .content {
	 display: none;
}

.is-editing .fal {
    font-family: 'primeicons';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-feature-settings: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.is-editing .fal.fa-external-link:before {
    content: "\e93c";
}

.is-editing .summary {
    display: block;
}
